import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber } from 'class-validator';

/**
 * DTO cho yêu cầu đăng nhập bằng Zalo OAuth2
 */
export class ZaloAuthDto {
  @ApiProperty({
    description: 'Authorization code từ Zalo OAuth2',
    example: 'AQD7veB2u5QZcM...',
  })
  @IsNotEmpty({ message: 'Authorization code không được để trống' })
  @IsString({ message: 'Authorization code phải là chuỗi' })
  code: string;

  @ApiProperty({
    description: 'URL chuyển hướng sau khi xác thực (tùy chọn)',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsString({ message: 'Redirect URI phải là chuỗi' })
  @IsOptional()
  redirectUri?: string;

  @ApiProperty({
    description: 'Mã người giới thiệu (tùy chọn)',
    example: 12345,
    required: false,
  })
  @IsNumber({}, { message: 'Mã người giới thiệu phải là số' })
  @IsOptional()
  ref?: number;

  @ApiProperty({
    description: 'State parameter để xác thực (tùy chọn)',
    example: 'random_state_string',
    required: false,
  })
  @IsString({ message: 'State phải là chuỗi' })
  @IsOptional()
  state?: string;
}

/**
 * DTO cho response của API lấy URL xác thực Zalo
 */
export class ZaloAuthUrlResponseDto {
  @ApiProperty({
    description: 'URL xác thực Zalo OAuth2',
    example: 'https://oauth.zaloapp.com/v4/permission?app_id=785984350482246426&redirect_uri=...',
  })
  url: string;

  @ApiProperty({
    description: 'State parameter được tạo tự động (nếu không cung cấp)',
    example: 'zalo_auth_1234567890',
    required: false,
  })
  state?: string;
}
