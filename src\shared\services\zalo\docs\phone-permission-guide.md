# Hướng Dẫn Lấy Số Điện Thoại Từ Zalo Social API

## 📋 **Tổng Quan**

Việc lấy số điện thoại từ Zalo Social API có một số **điều kiện và hạn chế** quan trọng mà bạn cần biết.

## ✅ **Điều Kiện Để Lấy Được Số Điện Thoại**

### 1. **App Phải Được Zalo Phê Duyệt**
- Quyền truy cập số điện thoại (`phone` scope) cần được Zalo review và approve
- Không phải app nào cũng được cấp quyền này
- Cần có lý do chính đáng và tuân thủ chính sách của Zalo

### 2. **Người Dùng Phải Đồng Ý**
- Người dùng phải chủ động cấp quyền chia sẻ số điện thoại
- <PERSON><PERSON> thể từ chối ngay cả khi app đã được phê duyệt

### 3. **Số Điện Thoại Phải Được Xác Thực**
- Người dùng phải đã xác thực số điện thoại trên tài khoản Zalo
- Nếu chưa xác thực, API sẽ không trả về số điện thoại

## 🔧 **Cách Implement**

### 1. **Request Phone Permission**

```typescript
// Tạo URL đăng nhập với quyền phone
const loginUrlWithPhone = zaloSocialService.createAuthUrlWithPhone(
  appId,
  redirectUri,
  'login_state'
);

// Hoặc tự tạo với scope tùy chỉnh
const customUrl = zaloSocialService.createAuthUrl(
  appId,
  redirectUri,
  'id,name,picture,phone', // Bao gồm phone scope
  'custom_state'
);
```

### 2. **Lấy Thông Tin Với Số Điện Thoại**

```typescript
// Sau khi có access token
const userInfo = await zaloSocialService.getUserInfoWithPhone(accessToken);

if (userInfo.phone) {
  console.log('Số điện thoại:', userInfo.phone);
  // Lưu vào database
  await this.saveUserPhone(userInfo.id, userInfo.phone);
} else {
  console.log('Không thể lấy số điện thoại');
  // Xử lý trường hợp không có số điện thoại
}
```

### 3. **Xử Lý An Toàn**

```typescript
async handleZaloCallback(code: string): Promise<{
  user: ZaloSocialUserInfo;
  hasPhone: boolean;
  message: string;
}> {
  try {
    // Lấy access token
    const tokens = await zaloSocialService.getAccessToken(
      appId, appSecret, code, redirectUri
    );

    // Lấy thông tin người dùng
    const userInfo = await zaloSocialService.getUserInfoWithPhone(tokens.access_token);

    if (userInfo.phone) {
      return {
        user: userInfo,
        hasPhone: true,
        message: 'Đăng nhập thành công với số điện thoại'
      };
    } else {
      return {
        user: userInfo,
        hasPhone: false,
        message: 'Đăng nhập thành công nhưng không có số điện thoại'
      };
    }
  } catch (error) {
    throw new Error(`Lỗi đăng nhập Zalo: ${error.message}`);
  }
}
```

## 🗄️ **Cấu Trúc Database**

### Cách 1: Lưu Trong User Entity (Đơn Giản)

```typescript
@Entity('users')
export class User {
  // ... các trường khác

  @Column({ name: 'zalo_user_id', length: 100, nullable: true, unique: true })
  zaloUserId: string;

  @Column({ name: 'zalo_phone', length: 20, nullable: true })
  zaloPhone: string; // Số điện thoại từ Zalo

  @Column({ name: 'zalo_name', length: 255, nullable: true })
  zaloName: string;

  @Column({ name: 'zalo_avatar_url', type: 'text', nullable: true })
  zaloAvatarUrl: string;
}
```

### Cách 2: Tạo Bảng Riêng (Linh Hoạt Hơn)

```typescript
@Entity('user_zalo_profiles')
export class UserZaloProfile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', unique: true })
  userId: number;

  @Column({ name: 'zalo_user_id', unique: true })
  zaloUserId: string;

  @Column({ name: 'zalo_phone', nullable: true })
  zaloPhone: string;

  @Column({ name: 'zalo_name' })
  zaloName: string;

  @Column({ name: 'zalo_avatar_url', nullable: true })
  zaloAvatarUrl: string;

  @Column({ name: 'access_token', type: 'text' })
  accessToken: string;

  @Column({ name: 'refresh_token', type: 'text' })
  refreshToken: string;

  @Column({ name: 'token_expires_at' })
  tokenExpiresAt: Date;

  @Column({ name: 'connected_at' })
  connectedAt: Date;
}
```

## ⚠️ **Lưu Ý Quan Trọng**

### 1. **Không Phải Lúc Nào Cũng Có Số Điện Thoại**
```typescript
// LUÔN kiểm tra trước khi sử dụng
if (userInfo.phone) {
  // Xử lý khi có số điện thoại
  await this.linkPhoneToUser(userInfo.phone);
} else {
  // Xử lý khi không có số điện thoại
  this.logger.warn('Người dùng không chia sẻ số điện thoại');
}
```

### 2. **Xử Lý Trùng Lặp**
```typescript
// Kiểm tra số điện thoại đã tồn tại chưa
const existingUser = await this.userRepository.findOne({
  where: [
    { zaloUserId: userInfo.id },
    { phoneNumber: userInfo.phone },
    { zaloPhone: userInfo.phone }
  ]
});

if (existingUser) {
  // Cập nhật thông tin Zalo cho user hiện tại
  await this.updateZaloInfo(existingUser, userInfo);
} else {
  // Tạo user mới
  await this.createUserFromZalo(userInfo);
}
```

### 3. **Bảo Mật Thông Tin**
```typescript
// Mã hóa số điện thoại nếu cần
const encryptedPhone = await this.encryptService.encrypt(userInfo.phone);

// Hoặc hash để so sánh
const phoneHash = await bcrypt.hash(userInfo.phone, 10);
```

## 🚀 **Best Practices**

### 1. **Fallback Strategy**
```typescript
async createOrUpdateUser(zaloUserInfo: ZaloSocialUserInfo): Promise<User> {
  let user: User;

  // Ưu tiên tìm theo Zalo User ID
  user = await this.findByZaloUserId(zaloUserInfo.id);
  
  if (!user && zaloUserInfo.phone) {
    // Tìm theo số điện thoại nếu có
    user = await this.findByPhone(zaloUserInfo.phone);
  }

  if (!user) {
    // Tạo user mới
    user = await this.createNewUser(zaloUserInfo);
  } else {
    // Cập nhật thông tin Zalo
    await this.updateZaloInfo(user, zaloUserInfo);
  }

  return user;
}
```

### 2. **Logging & Monitoring**
```typescript
this.logger.log(`Zalo login - User: ${userInfo.name}, Phone: ${userInfo.phone ? 'Yes' : 'No'}`);

// Metrics để theo dõi tỷ lệ người dùng chia sẻ số điện thoại
this.metricsService.increment('zalo_login_total');
if (userInfo.phone) {
  this.metricsService.increment('zalo_login_with_phone');
}
```

## 📞 **Kết Luận**

- ✅ **CÓ THỂ** lấy số điện thoại từ Zalo
- ⚠️ **CẦN** app được phê duyệt và người dùng đồng ý
- 🔄 **NÊN** có fallback strategy khi không có số điện thoại
- 🛡️ **PHẢI** xử lý bảo mật và trùng lặp dữ liệu

Việc lấy số điện thoại từ Zalo là **có thể** nhưng **không đảm bảo 100%**, vì vậy hệ thống cần được thiết kế để hoạt động tốt cả khi có và không có số điện thoại.
