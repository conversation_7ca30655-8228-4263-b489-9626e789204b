# Zalo Authentication API

## 📋 **Tổng Quan**

Hệ thống đã được tích hợp đầy đủ Zalo OAuth2 V4 để cho phép người dùng đăng nhập bằng tài khoản <PERSON>alo.

## 🔧 **C<PERSON>u <PERSON>**

### Thông Tin Ứng Dụng Zalo
- **App ID**: `785984350482246426`
- **App Secret**: `4wClko3npQPavdlJHk76`
- **Redirect URI**: <PERSON><PERSON> thể cấu hình qua biến môi trường `ZALO_REDIRECT_URI` (mặc định: `http://localhost:3000/auth/zalo/callback`)

## 🚀 **API Endpoints**

### 1. **Lấy URL Xác Thực <PERSON>**

**GET** `/auth/zalo/auth-url`

Lấy URL để chuyển hướng người dùng đến trang đăng nhập <PERSON>.

#### Query Parameters:
- `redirectUri` (optional): URL chuyển hướng sau khi xác thực
- `state` (optional): State parameter để bảo mật

#### Response:
```json
{
  "code": 200,
  "message": "Lấy URL xác thực Zalo thành công",
  "result": {
    "url": "https://oauth.zaloapp.com/v4/permission?app_id=785984350482246426&redirect_uri=...",
    "state": "zalo_auth_1234567890"
  }
}
```

#### Ví dụ sử dụng:
```bash
curl -X GET "http://localhost:3000/auth/zalo/auth-url?redirectUri=http://localhost:3000/auth/zalo/callback"
```

### 2. **Đăng Nhập Bằng Zalo**

**POST** `/auth/zalo/login`

Xử lý authorization code từ Zalo và tạo JWT token cho người dùng.

#### Request Body:
```json
{
  "code": "AQD7veB2u5QZcM...",
  "redirectUri": "http://localhost:3000/auth/zalo/callback",
  "ref": 12345,
  "state": "zalo_auth_1234567890"
}
```

#### Response:
```json
{
  "code": 200,
  "message": "Đăng nhập Zalo thành công",
  "result": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": 1746968772000,
    "info": [],
    "user": {
      "id": 1,
      "email": "",
      "username": "",
      "permissions": ["read:profile", "write:profile"],
      "status": "active"
    }
  }
}
```

#### Headers:
- **Set-Cookie**: `refresh_token=...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800`

## 🔄 **Flow Đăng Nhập Hoàn Chỉnh**

### Bước 1: Frontend lấy URL xác thực
```javascript
const response = await fetch('/auth/zalo/auth-url');
const { result } = await response.json();
const { url, state } = result;

// Chuyển hướng người dùng đến URL Zalo
window.location.href = url;
```

### Bước 2: Người dùng đăng nhập trên Zalo
Người dùng sẽ được chuyển hướng đến trang đăng nhập Zalo và cấp quyền cho ứng dụng.

### Bước 3: Zalo chuyển hướng về callback URL
Zalo sẽ chuyển hướng về `redirectUri` với authorization code:
```
http://localhost:3000/auth/zalo/callback?code=AQD7veB2u5QZcM...&state=zalo_auth_1234567890
```

### Bước 4: Frontend gửi code đến backend
```javascript
const urlParams = new URLSearchParams(window.location.search);
const code = urlParams.get('code');
const state = urlParams.get('state');

const response = await fetch('/auth/zalo/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    code,
    redirectUri: 'http://localhost:3000/auth/zalo/callback',
    state
  }),
});

const { result } = await response.json();
const { accessToken, user } = result;

// Lưu access token và thông tin người dùng
localStorage.setItem('accessToken', accessToken);
localStorage.setItem('user', JSON.stringify(user));
```

## 🗄️ **Dữ Liệu Được Lưu**

Khi người dùng đăng nhập bằng Zalo, hệ thống sẽ lưu:

### Trong User Entity:
- `zaloUserId`: ID duy nhất từ Zalo
- `zaloName`: Tên hiển thị từ Zalo
- `zaloAvatarUrl`: URL avatar từ Zalo
- `zaloAccessToken`: Access token để gọi Zalo API
- `zaloRefreshToken`: Refresh token để làm mới access token
- `zaloTokenExpiresAt`: Thời gian hết hạn của Zalo token
- `zaloConnectedAt`: Thời gian kết nối Zalo lần đầu
- `phoneNumber`: Số điện thoại (nếu có quyền)

## ⚠️ **Lưu Ý Quan Trọng**

### 1. **Xử Lý Người Dùng Mới vs Hiện Tại**
- Nếu `zaloUserId` đã tồn tại → Cập nhật thông tin Zalo
- Nếu số điện thoại đã tồn tại → Liên kết tài khoản hiện tại với Zalo
- Nếu hoàn toàn mới → Tạo tài khoản mới

### 2. **Quyền Truy Cập**
- Hiện tại chỉ lấy được: `id`, `name`, `picture`
- Để lấy `phone`, cần app được Zalo phê duyệt

### 3. **Bảo Mật**
- State parameter được sử dụng để chống CSRF
- Refresh token được lưu trong HTTP-only cookie
- Access token có thời hạn 1 giờ

### 4. **Error Handling**
```json
{
  "code": 400,
  "message": "Đăng nhập Zalo thất bại",
  "result": null
}
```

## 🧪 **Testing**

### Test URL Generation:
```bash
curl -X GET "http://localhost:3000/auth/zalo/auth-url"
```

### Test Login (cần authorization code thực):
```bash
curl -X POST "http://localhost:3000/auth/zalo/login" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "REAL_AUTHORIZATION_CODE_FROM_ZALO",
    "redirectUri": "http://localhost:3000/auth/zalo/callback"
  }'
```

## 📚 **Tài Liệu Tham Khảo**

- [Zalo User Access Token V4](https://developers.zalo.me/docs/social-api/tham-khao/user-access-token-v4)
- [Zalo Social API Documentation](https://developers.zalo.me/docs/api/social-api)
- [Zalo Developers Portal](https://developers.zalo.me/)

## 🔧 **Troubleshooting**

### Lỗi "Thiếu cấu hình Zalo App"
- Kiểm tra App ID và App Secret đã được cấu hình đúng
- Kiểm tra Redirect URI khớp với cấu hình trên Zalo Developers

### Lỗi "Authorization code không hợp lệ"
- Code chỉ sử dụng được 1 lần
- Code có thời hạn ngắn (thường 10 phút)
- Kiểm tra Redirect URI phải giống nhau ở cả 2 bước

### Lỗi "Không thể lấy thông tin người dùng"
- Kiểm tra access token có hợp lệ
- Kiểm tra quyền scope đã được cấp đúng
