/**
 * Interface định nghĩa cấu trúc phản hồi chuẩn từ Zalo API
 */
export interface ZaloResponse<T> {
  /**
   * Mã trạng thái phản hồi
   */
  error: number;

  /**
   * Thông báo lỗi (nếu có)
   */
  message: string;

  /**
   * Dữ liệu phản hồi
   */
  data?: T;
}

/**
 * Interface định nghĩa thông tin access token
 */
export interface ZaloAccessToken {
  /**
   * Access token
   */
  access_token: string;

  /**
   * Thời gian hết hạn (tính bằng giây)
   */
  expires_in: number;

  /**
   * Refresh token (nếu có)
   */
  refresh_token?: string;
}

/**
 * Interface định nghĩa phản hồi khi làm mới access token từ refresh token
 */
export interface ZaloRefreshTokenResponse {
  /**
   * Access token mới
   */
  access_token: string;

  /**
   * Thời gian hết hạn của access token (tính bằng giây)
   */
  expires_in: number;

  /**
   * Refresh token mới (nếu có)
   */
  refresh_token?: string;
}

/**
 * Interface định nghĩa thông tin chi tiết người dùng từ Social API
 */
export interface ZaloSocialUserInfo {
  /**
   * ID của người dùng Zalo
   */
  id: string;

  /**
   * Tên hiển thị của người dùng
   */
  name: string;

  /**
   * URL avatar của người dùng
   */
  picture: {
    data: {
      url: string;
    };
  };

  /**
   * Giới tính của người dùng
   */
  gender?: string;

  /**
   * Ngày sinh của người dùng
   */
  birthday?: string;

  /**
   * Địa chỉ của người dùng (nếu được cấp quyền)
   */
  location?: {
    name: string;
  };

  /**
   * Số điện thoại của người dùng (nếu được cấp quyền)
   * Chỉ có khi app được Zalo phê duyệt và người dùng đồng ý chia sẻ
   */
  phone?: string;
}

/**
 * Interface định nghĩa thông tin Official Account
 */
export interface ZaloOaInfo {
  /**
   * ID của Official Account
   */
  oa_id: string;

  /**
   * Tên của Official Account
   */
  name: string;

  /**
   * Mô tả của Official Account
   */
  description: string;

  /**
   * URL avatar của Official Account
   */
  avatar: string;

  /**
   * Trạng thái của Official Account
   */
  status: string;
}

/**
 * Interface định nghĩa thông tin người dùng Zalo
 */
export interface ZaloUserInfo {
  /**
   * ID của người dùng Zalo
   */
  id: string;

  /**
   * Tên hiển thị của người dùng
   */
  display_name: string;

  /**
   * URL avatar của người dùng
   */
  avatar: string;

  /**
   * Giới tính của người dùng (1: Nam, 2: Nữ)
   */
  gender?: number;

  /**
   * Ngày sinh của người dùng (định dạng dd/mm/yyyy)
   */
  birth_date?: string;

  /**
   * Số điện thoại của người dùng (nếu được cấp quyền)
   */
  phone?: string;
}

/**
 * Interface định nghĩa tin nhắn văn bản
 */
export interface ZaloTextMessage {
  /**
   * Loại tin nhắn (text)
   */
  type: 'text';

  /**
   * Nội dung tin nhắn
   */
  text: string;
}

/**
 * Interface định nghĩa tin nhắn hình ảnh
 */
export interface ZaloImageMessage {
  /**
   * Loại tin nhắn (image)
   */
  type: 'image';

  /**
   * URL của hình ảnh
   */
  url: string;

  /**
   * Chú thích cho hình ảnh (nếu có)
   */
  caption?: string;
}

/**
 * Interface định nghĩa tin nhắn tệp đính kèm
 */
export interface ZaloFileMessage {
  /**
   * Loại tin nhắn (file)
   */
  type: 'file';

  /**
   * URL của tệp đính kèm
   */
  url: string;

  /**
   * Tên của tệp đính kèm
   */
  name: string;
}

/**
 * Interface định nghĩa tin nhắn template
 */
export interface ZaloTemplateMessage {
  /**
   * Loại tin nhắn (template)
   */
  type: 'template';

  /**
   * ID của template
   */
  template_id: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, any>;
}

/**
 * Union type cho các loại tin nhắn
 */
export type ZaloMessage = ZaloTextMessage | ZaloImageMessage | ZaloFileMessage | ZaloTemplateMessage;

/**
 * Interface định nghĩa thông tin sự kiện webhook
 */
export interface ZaloWebhookEvent {
  /**
   * ID của sự kiện
   */
  event_id: string;

  /**
   * Tên của sự kiện
   */
  event_name: string;

  /**
   * Thời gian xảy ra sự kiện (Unix timestamp)
   */
  timestamp: number;

  /**
   * Dữ liệu của sự kiện
   */
  data: any;
}

/**
 * Interface định nghĩa thông tin tin nhắn ZNS
 */
export interface ZaloZnsMessage {
  /**
   * ID của template
   */
  template_id: string;

  /**
   * Số điện thoại người nhận
   */
  phone: string;

  /**
   * Dữ liệu cho template
   */
  template_data: Record<string, string>;

  /**
   * ID giao dịch (nếu có)
   */
  tracking_id?: string;
}

/**
 * Interface định nghĩa kết quả gửi tin nhắn ZNS
 */
export interface ZaloZnsSendResult {
  /**
   * ID của tin nhắn
   */
  message_id: string;

  /**
   * ID giao dịch
   */
  tracking_id: string;
}
