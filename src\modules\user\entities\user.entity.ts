import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { GenderEnum, UserTypeEnum } from '../enums';

/**
 * Entity đại diện cho bảng users trong cơ sở dữ liệu
 * Bảng tài khoản của người dùng
 */
@Entity('users')
export class User {
  /**
   * ID của người dùng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên đầy đủ của người dùng
   */
  @Column({ name: 'full_name', length: 100, nullable: true, comment: 'tên đầy đủ của người dùng' })
  fullName: string;

  /**
   * Email của người dùng
   */
  @Column({ name: 'email', length: 100, nullable: true, unique: true, comment: 'email của người dùng' })
  email: string;

  /**
   * <PERSON><PERSON> điện thoại của người dùng
   */
  @Column({ name: 'phone_number', length: 45, nullable: true, unique: true, comment: 'số điện thoại của người dùng' })
  phoneNumber: string;

  /**
   * Trạng thái tài khoản
   */
  @Column({ name: 'is_active', default: true, comment: 'trạng thái tài khoản' })
  isActive: boolean;

  /**
   * Trạng thái xác thực email
   */
  @Column({ name: 'is_verify_email', default: false, comment: 'trạng thái xác thực' })
  isVerifyEmail: boolean;

  /**
   * Thời gian tạo tài khoản (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'thời gian tạo tài khoản' })
  createdAt: number;

  /**
   * Thời gian cập nhật thông tin (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'thời gian cập nhật thông tin' })
  updatedAt: number;

  /**
   * Nơi cấp chứng minh nhân dân
   */
  @Column({ name: 'citizen_issue_place', length: 100, nullable: true, comment: 'Nơi cấp chứng minh nhân dân' })
  citizenIssuePlace: string;

  /**
   * Ngày cấp chứng minh nhân dân
   */
  @Column({ name: 'citizen_issue_date', type: 'date', nullable: true, comment: 'ngày cấp chứng minh nhân dân' })
  citizenIssueDate: Date;

  /**
   * Đánh dấu lần thay đổi mật khẩu đầu tiên
   */
  @Column({ name: 'is_first_password_change', default: false, comment: 'đánh dấu lần thay đổi mật khẩu đầu tiên' })
  isFirstPasswordChange: boolean;

  /**
   * Đất nước
   */
  @Column({ name: 'country', type: 'int', nullable: true, comment: 'đất nước' })
  country: number;

  /**
   * Địa chỉ
   */
  @Column({ name: 'address', length: 1000, nullable: true, comment: 'địa chỉ' })
  address: string;

  /**
   * Mã số thuế
   */
  @Column({ name: 'tax_code', length: 20, nullable: true, comment: 'mã số thuế' })
  taxCode: string;

  /**
   * Số dư points hiện tại
   */
  @Column({ name: 'points_balance', type: 'bigint', default: 0, comment: 'Số dư points hiện tại' })
  pointsBalance: number;


  /**
   * Mã facebook
   */
  @Column({ name: 'facebook_id', type: 'varchar', comment: 'Facebook ID', nullable: true, unique: true })
  facebookId: string;

  /**
   * Mã google
   */
  @Column({ name: 'google_id', type: 'varchar', comment: 'Google ID', nullable: true, unique: true })
  googleId: string;

  /**
   * Loại tài khoản ('INDIVIDUAL' hoặc 'BUSINESS')
   */
  @Column({
    name: 'type',
    type: 'enum',
    enum: UserTypeEnum,
    default: UserTypeEnum.INDIVIDUAL,
    comment: "loại tài khoản 'INDIVIDUAL' hoặc'BUSINESS'"
  })
  type: UserTypeEnum;

  @Column({ name: 'platform', length: 20, nullable: true })
  platform: string;

  @Column({ name: 'citizen_id', length: 20, nullable: true })
  citizenId: string;

  @Column({ name: 'avatar', length: 255, nullable: true })
  avatar: string;

  @Column({ name: 'password', length: 1000, nullable: true })
  password: string;

  @Column({ name: 'date_of_birth', type: 'date', nullable: true })
  dateOfBirth: Date;

  @Column({ name: 'gender', type: 'enum', enum: GenderEnum, nullable: true })
  gender: GenderEnum;

  @Column({ name: 'bank_code', length: 20, nullable: true })
  bankCode: string;

  @Column({ name: 'account_number', length: 50, nullable: true })
  accountNumber: string;

  @Column({ name: 'account_holder', length: 255, nullable: true })
  accountHolder: string;

  /**
   * ID người dùng Zalo (duy nhất từ Zalo API)
   */
  @Column({ name: 'zalo_user_id', length: 100, nullable: true, unique: true, comment: 'ID người dùng từ Zalo API' })
  zaloUserId: string;

  /**
   * Access token để gọi Zalo API
   */
  @Column({ name: 'zalo_access_token', type: 'text', nullable: true, comment: 'Access token từ Zalo API' })
  zaloAccessToken: string;

  /**
   * Refresh token để làm mới access token
   */
  @Column({ name: 'zalo_refresh_token', type: 'text', nullable: true, comment: 'Refresh token từ Zalo API' })
  zaloRefreshToken: string;

  /**
   * Thời gian hết hạn của Zalo access token
   */
  @Column({ name: 'zalo_token_expires_at', type: 'timestamp', nullable: true, comment: 'Thời gian hết hạn Zalo access token' })
  zaloTokenExpiresAt: Date;

  /**
   * Tên hiển thị từ Zalo
   */
  @Column({ name: 'zalo_name', length: 255, nullable: true, comment: 'Tên hiển thị từ Zalo' })
  zaloName: string;

  /**
   * URL avatar từ Zalo
   */
  @Column({ name: 'zalo_avatar_url', type: 'text', nullable: true, comment: 'URL avatar từ Zalo' })
  zaloAvatarUrl: string;

  /**
   * Thời gian kết nối Zalo lần đầu
   */
  @Column({ name: 'zalo_connected_at', type: 'timestamp', nullable: true, comment: 'Thời gian kết nối Zalo lần đầu' })
  zaloConnectedAt: Date;

  @Column({ name: 'bank_branch', length: 255, nullable: true })
  bankBranch: string;

  @Column({ name: 'is_verify_phone', nullable: true })
  isVerifyPhone: boolean;

  @Column({ name: 'alert_threshold', type: 'bigint', nullable: true })
  alertThreshold: number;

  @Column({ name: 'was_rpoint_alerted', nullable: true })
  wasRpointAlerted: boolean;

  @Column({ name: 'affiliate_account_id', nullable: true })
  affiliateAccountId: number;

  /**
   * Mã quốc gia của số điện thoại
   */
  @Column({ name: 'country_code', length: 10, default: '+84', nullable: true, comment: 'Mã quốc gia của số điện thoại người dùng' })
  countryCode: string;
}
